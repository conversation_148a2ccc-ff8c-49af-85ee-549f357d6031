"use client";

import Image from "next/image";
import Header from "@/components/layout/Header";
import Footer from "@/components/layout/Footer";
import TeamSection from "@/components/sections/TeamSection";

export default function OurTeamPage() {
  return (
    <div className="min-h-screen flex flex-col bg-black-950">
      <Header />
      
      <main className="flex-1">

            {/* Hero Banner */}
        <section className="py-16 mt-16 md:py-20 bg-black-600 bg-meditation cosmic-bg relative">
          <div className="absolute inset-0 z-0">
            <div className="absolute top-0 left-0 w-full h-64 bg-gradient-to-b from-black-950 to-transparent" />
            <div className="absolute bottom-0 left-0 w-full h-64 bg-gradient-to-t from-black-950 to-transparent" />
          </div>

          <div className="container mx-auto px-4 relative z-10">
            <div className="max-w-4xl mx-auto text-center animate-fade-in">
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
                <span className="text-white">Meet Our </span>
                <span className="text-gradient">Spiritual Guide</span>
              </h1>
              <p className="text-lg text-white mb-8">
                Discover the visionary behind Wild World Wanderers' transformative journeys
              </p>
            </div>
          </div>
        </section>
        <section className="py-16 sm:py-20 bg-black-500 relative overflow-hidden">
          {/* Background Elements */}
          <div className="absolute inset-0 opacity-5">
            <Image
              src="/images/gallery/photo-1527786356703-4b100091cd2c.jpg"
              alt="Spiritual Background"
              fill
              className="object-cover"
              crossOrigin="anonymous"
            />
          </div>
          
          {/* Gradient Overlays */}
          <div className="absolute inset-0">
            <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-black-900/80 via-black-800/60 to-black-900/80" />
            <div className="absolute top-1/4 left-0 w-96 h-96 rounded-full bg-gold-500/10 blur-3xl" />
            <div className="absolute bottom-1/4 right-0 w-96 h-96 rounded-full bg-purple-500/10 blur-3xl" />
          </div>

          <div className="container mx-auto px-4 sm:px-6 relative z-10">
            <div className="max-w-6xl mx-auto">
            

              {/* Team Content */}
              <TeamSection />
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
}
