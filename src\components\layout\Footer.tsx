"use client";

import Link from "next/link";
import { Facebook, Instagram, Twitter, Youtube, Mail, Phone, MapPin } from "lucide-react";
import Image from "next/image";



const socialLinks = [
  { icon: Facebook, href: "#", label: "Facebook" },
  { icon: Instagram, href: "#", label: "Instagram" },
  { icon: Twitter, href: "#", label: "Twitter" },
  { icon: Youtube, href: "#", label: "Youtube" },
];

export default function Footer() {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-black-950 relative overflow-hidden cosmic-bg mt-auto">
      {/* Background gradient elements */}
      <div className="absolute inset-0 z-0">
        <div className="absolute bottom-0 left-0 w-full h-64 bg-gradient-to-t from-black-950 to-transparent" />
        <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-white/20 to-transparent" />
        <div className="absolute top-1/4 left-0 w-64 h-64 rounded-full bg-blue-500/5 blur-3xl" />
        <div className="absolute bottom-1/4 right-0 w-96 h-96 rounded-full bg-purple-500/5 blur-3xl" />
      </div>

      <div className="container mx-auto py-12 sm:py-16 px-4 sm:px-6 relative z-10">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8 lg:gap-12">
          {/* Logo and Description */}
          <div className="lg:col-span-1">
            <Link href="/" className="pb-4 flex items-center mb-4 sm:mb-6 outline-none focus-visible:ring-2 focus-visible:ring-gold-500 rounded-lg">
              <div className="relative w-40 h-10">
                <Image
                  src="/logo.webp"
                  alt="Wild World Wanderers Logo"
                  width={80}
                  height={40}
                  className="object-contain"
                />
              </div>
            </Link>
            <p className="mb-6 text-white max-w-sm text-sm sm:text-base">
              In a world of WWW (World Wide Web), we aim to find the WWW (Wild World Wanderers)
              in you. Join us for transformative journeys in the breathtaking beauty of Kashmir.
            </p>
            <div className="flex space-x-4">
              {socialLinks.map((social) => (
                <a
                  key={social.label}
                  href={social.href}
                  aria-label={social.label}
                  className="bg-white/5 hover:bg-gold-500 text-white hover:text-black-900 p-2 sm:p-3 rounded-full transition-colors duration-300 flex items-center justify-center min-w-[44px] min-h-[44px]"
                >
                  <social.icon className="h-5 w-5" />
                </a>
              ))}
            </div>
          </div>

          {/* Contact Information */}
          <div className="lg:col-span-3">
            <h2 className="text-2xl sm:text-3xl font-bold text-gradient mb-8">Get In Touch</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {/* Email */}
              <div className="bg-black-900/40 backdrop-blur-sm border border-white/10 rounded-xl p-6 hover:border-gold-500/30 transition-all duration-300">
                <div className="flex items-center mb-4">
                  <div className="p-3 rounded-full bg-gradient-to-br from-gold-500/20 to-red-500/20 flex-shrink-0 mr-4">
                    <Mail className="h-6 w-6 text-gold-500" />
                  </div>
                  <h3 className="text-white font-semibold text-lg">Email Us</h3>
                </div>
                <a
                  href="mailto:<EMAIL>"
                  className="text-white/80 hover:text-gold-500 transition-colors text-sm sm:text-base break-all"
                >
                  <EMAIL>
                </a>
              </div>

              {/* Phone */}
              <div className="bg-black-900/40 backdrop-blur-sm border border-white/10 rounded-xl p-6 hover:border-gold-500/30 transition-all duration-300">
                <div className="flex items-center mb-4">
                  <div className="p-3 rounded-full bg-gradient-to-br from-gold-500/20 to-red-500/20 flex-shrink-0 mr-4">
                    <Phone className="h-6 w-6 text-gold-500" />
                  </div>
                  <h3 className="text-white font-semibold text-lg">Call Us</h3>
                </div>
                <a
                  href="tel:9419955663"
                  className="text-white/80 hover:text-gold-500 transition-colors text-lg font-medium"
                >
                  9419955663
                </a>
              </div>

              {/* Address */}
              <div className="bg-black-900/40 backdrop-blur-sm border border-white/10 rounded-xl p-6 hover:border-gold-500/30 transition-all duration-300">
                <div className="flex items-center mb-4">
                  <div className="p-3 rounded-full bg-gradient-to-br from-gold-500/20 to-red-500/20 flex-shrink-0 mr-4">
                    <MapPin className="h-6 w-6 text-gold-500" />
                  </div>
                  <h3 className="text-white font-semibold text-lg">Visit Us</h3>
                </div>
                <p className="text-white/80 text-sm sm:text-base leading-relaxed">
                  1st Floor, Pulloo Complex,<br />
                  Munawara Abad, Srinagar,<br />
                  J&K 190001
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Copyright */}
        <div className="border-t border-white/10 mt-10 sm:mt-12 pt-6 sm:pt-8 flex flex-col sm:flex-row justify-between items-center">
          <p className="text-white/60 text-xs sm:text-sm mb-4 sm:mb-0 text-center sm:text-left">
            © {currentYear} Wild World Wanderers. All rights reserved.
          </p>
          <div className="flex flex-wrap justify-center gap-4 sm:gap-6">
            <Link
              href="/experiences"
              className="text-white/60 hover:text-gold-500 text-xs sm:text-sm transition-colors px-2 py-1"
            >
              Experiences
            </Link>
            <Link
              href="/spiritual-journeys"
              className="text-white/60 hover:text-gold-500 text-xs sm:text-sm transition-colors px-2 py-1"
            >
              Spiritual Journeys
            </Link>
            <Link
              href="/our-team"
              className="text-white/60 hover:text-gold-500 text-xs sm:text-sm transition-colors px-2 py-1"
            >
              Our Team
            </Link>
            <Link
              href="/contact"
              className="text-white/60 hover:text-gold-500 text-xs sm:text-sm transition-colors px-2 py-1"
            >
              Contact
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
}
