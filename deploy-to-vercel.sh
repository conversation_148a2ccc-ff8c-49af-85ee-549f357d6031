#!/bin/bash

# Wild World Wanderers - Vercel Deployment Script
# This script helps deploy the website to Vercel with proper SMTP configuration

echo "🚀 Wild World Wanderers - Vercel Deployment"
echo "============================================"

# Check if Vercel CLI is installed
if ! command -v vercel &> /dev/null; then
    echo "❌ Vercel CLI is not installed. Please install it first:"
    echo "   npm i -g vercel"
    exit 1
fi

echo "✅ Vercel CLI found"

# Check if user is logged in to Vercel
if ! vercel whoami &> /dev/null; then
    echo "🔐 Please log in to Vercel:"
    vercel login
fi

echo "✅ Vercel authentication verified"

# Build the project locally first
echo "🔨 Building project locally..."
npm run build

if [ $? -ne 0 ]; then
    echo "❌ Local build failed. Please fix errors before deploying."
    exit 1
fi

echo "✅ Local build successful"

# Check if environment variables are set
echo "🔍 Checking environment variables..."

# Check local environment
if [ -f ".env.local" ]; then
    echo "✅ Local .env.local file found"
    
    if grep -q "SMTP_EMAIL=" .env.local && grep -q "SMTP_PASSWORD=" .env.local; then
        echo "✅ SMTP variables found in local environment"
    else
        echo "⚠️  SMTP variables not found in .env.local"
        echo "   Please ensure SMTP_EMAIL and SMTP_PASSWORD are set"
    fi
else
    echo "⚠️  No .env.local file found"
fi

# Prompt user to set Vercel environment variables
echo ""
echo "📝 IMPORTANT: Ensure these environment variables are set in Vercel:"
echo "   SMTP_EMAIL=<EMAIL>"
echo "   SMTP_PASSWORD=your_gmail_app_password"
echo ""
echo "   To set them:"
echo "   1. Go to Vercel Dashboard → Your Project → Settings → Environment Variables"
echo "   2. Add both variables for Production, Preview, and Development"
echo "   3. Or use: vercel env add SMTP_EMAIL"
echo "              vercel env add SMTP_PASSWORD"
echo ""

read -p "Have you set the SMTP environment variables in Vercel? (y/n): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ Please set the environment variables first, then run this script again."
    exit 1
fi

# Deploy to Vercel
echo "🚀 Deploying to Vercel..."
vercel --prod

if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 Deployment successful!"
    echo ""
    echo "📧 To test SMTP functionality:"
    echo "   1. Visit your deployed website"
    echo "   2. Go to /api/test-smtp to verify SMTP configuration"
    echo "   3. Test the contact form on homepage or /contact page"
    echo ""
    echo "🔍 To monitor function logs:"
    echo "   vercel logs --follow"
    echo ""
    echo "📚 For troubleshooting, see: VERCEL_SMTP_SETUP.md"
else
    echo "❌ Deployment failed. Check the error messages above."
    exit 1
fi
