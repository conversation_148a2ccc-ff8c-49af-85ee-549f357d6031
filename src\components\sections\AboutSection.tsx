"use client";

import Image from "next/image";
import TeamSection from "./TeamSection";

export default function AboutSection() {
  return (
    <section className="py-16 sm:py-20 bg-black-500 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 opacity-5">
        <Image
          src="/images/gallery/photo-1527786356703-4b100091cd2c.jpg"
          alt="Spiritual Background"
          fill
          className="object-cover"
          crossOrigin="anonymous"
        />
      </div>
      
      {/* Gradient Overlays */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-black-900/80 via-black-800/60 to-black-900/80" />
        <div className="absolute top-1/4 left-0 w-96 h-96 rounded-full bg-gold-500/10 blur-3xl" />
        <div className="absolute bottom-1/4 right-0 w-96 h-96 rounded-full bg-purple-500/10 blur-3xl" />
      </div>

      <div className="container mx-auto px-4 sm:px-6 relative z-10">
        <div className="max-w-6xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-12 sm:mb-16">
            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-white mb-4">
              Meet Our <span className="text-gradient">Spiritual Guide</span>
            </h2>
            <p className="text-white/70 text-lg sm:text-xl max-w-3xl mx-auto">
              Discover the visionary behind Wild World Wanderers' transformative journeys
            </p>
          </div>

          {/* Main Content */}
          <TeamSection />
        </div>
      </div>
    </section>
  );
}
