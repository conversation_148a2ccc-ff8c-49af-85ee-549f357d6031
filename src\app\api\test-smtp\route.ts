import { NextResponse } from 'next/server';
import nodemailer from 'nodemailer';

export async function GET() {
  try {
    // Check if SMTP credentials are configured
    if (!process.env.SMTP_EMAIL || !process.env.SMTP_PASSWORD) {
      return NextResponse.json(
        { 
          status: 'error',
          message: 'SMTP credentials not configured',
          details: {
            emailConfigured: !!process.env.SMTP_EMAIL,
            passwordConfigured: !!process.env.SMTP_PASSWORD
          }
        },
        { status: 500 }
      );
    }

    // Create transporter
    const transporter = nodemailer.createTransport({
      host: 'smtp.gmail.com',
      port: 587,
      secure: false,
      auth: {
        user: process.env.SMTP_EMAIL,
        pass: process.env.SMTP_PASSWORD,
      },
      tls: {
        rejectUnauthorized: false
      }
    });

    // Test connection
    await transporter.verify();

    return NextResponse.json(
      {
        status: 'success',
        message: 'SMTP configuration is working correctly',
        details: {
          smtpHost: 'smtp.gmail.com',
          smtpPort: 587,
          emailAccount: process.env.SMTP_EMAIL,
          timestamp: new Date().toISOString()
        }
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('SMTP test error:', error);
    
    return NextResponse.json(
      {
        status: 'error',
        message: 'SMTP configuration test failed',
        details: {
          error: error instanceof Error ? error.message : 'Unknown error',
          timestamp: new Date().toISOString()
        }
      },
      { status: 500 }
    );
  }
}
