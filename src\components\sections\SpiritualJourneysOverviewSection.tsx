"use client";

import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import Image from "next/image";
import { MapPin, Calendar, Users, Clock, CheckCircle, ArrowRight } from "lucide-react";

export default function SpiritualJourneysOverviewSection() {
  return (
    <section className="py-16 md:py-20 bg-black-500 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 opacity-5">
        <Image
          src="/images/gallery/photo-1527786356703-4b100091cd2c.jpg"
          alt="Sacred Kashmir Background"
          fill
          className="object-cover"
          crossOrigin="anonymous"
        />
      </div>
      
      {/* Gradient Overlays */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-black-900/90 via-black-800/80 to-black-900/90" />
        <div className="absolute top-1/4 left-0 w-96 h-96 rounded-full bg-gold-500/10 blur-3xl" />
        <div className="absolute bottom-1/4 right-0 w-96 h-96 rounded-full bg-purple-500/10 blur-3xl" />
      </div>

      <div className="container mx-auto px-4 sm:px-6 relative z-10">
        <div className="max-w-6xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-12 sm:mb-16">
            <div className="inline-block mb-3 px-3 py-1 bg-gradient-to-r from-gold-500/20 to-purple-500/20 rounded-full">
              <span className="text-white text-sm font-medium">Sacred Kashmir</span>
            </div>
            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-white mb-4">
              Spiritual <span className="text-gradient">Journeys</span>
            </h2>
            <p className="text-white/70 text-lg sm:text-xl max-w-3xl mx-auto">
              A transformative 15-day spiritual journey through the mystical landscapes of Kashmir
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center">
            {/* Left Content */}
            <div className="space-y-6">
              {/* Tour Overview Cards */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div className="bg-black-900/40 backdrop-blur-sm border border-white/10 rounded-xl p-4">
                  <div className="flex items-center space-x-3 mb-2">
                    <Clock className="w-5 h-5 text-gold-500" />
                    <h3 className="text-white font-semibold text-sm">Duration</h3>
                  </div>
                  <p className="text-white/80 text-sm">15 Days 14 Nights</p>
                </div>

                <div className="bg-black-900/40 backdrop-blur-sm border border-white/10 rounded-xl p-4">
                  <div className="flex items-center space-x-3 mb-2">
                    <MapPin className="w-5 h-5 text-gold-500" />
                    <h3 className="text-white font-semibold text-sm">Destinations</h3>
                  </div>
                  <p className="text-white/80 text-sm">Srinagar, Anantnag, Ganderbal</p>
                </div>

     
              </div>

              {/* Key Highlights */}
              <div className="bg-black-900/40 backdrop-blur-sm border border-white/10 rounded-xl p-6">
                <h3 className="text-white font-semibold text-lg mb-4">Journey Highlights</h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                  {[
                    "Sacred Pilgrimage to Samkaropala",
                    "Meditation & Satsang with Andrew Harvey",
                    "Shikara Ride in Dal Lake",
                    "Visit to Ancient Temples & Shrines",
                    "Sufi Music Night",
                    "Traditional Wazwan Experience"
                  ].map((highlight, index) => (
                    <div key={index} className="flex items-start space-x-2">
                      <CheckCircle className="w-4 h-4 text-gold-500 mt-0.5 flex-shrink-0" />
                      <p className="text-white/80 text-sm">{highlight}</p>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Right Content - Image */}
            <div className="relative">
              <div className="relative rounded-2xl overflow-hidden">
                <Image
                  src="/images/gallery/photo-1583249598754-110018cb6851.jpg"
                  alt="Sacred Kashmir Journey"
                  width={600}
                  height={400}
                  className="object-cover w-full h-[400px]"
                  crossOrigin="anonymous"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                
                {/* Floating Quote */}
                <div className="absolute bottom-6 left-6 right-6">
                  <div className="bg-black/80 backdrop-blur-sm rounded-xl p-4 border border-gold-500/20">
                    <p className="text-gold-400 font-medium italic text-sm mb-2">
                      "A journey to awaken the divine human potential within"
                    </p>
                    <p className="text-white/60 text-xs">
                      Experience the mystical valleys where ancient wisdom meets modern spiritual practice
                    </p>
                  </div>
                </div>
              </div>

              {/* Decorative Elements */}
              <div className="absolute -top-4 -right-4 w-24 h-24 rounded-full bg-gold-500/20 blur-2xl"></div>
              <div className="absolute -bottom-4 -left-4 w-32 h-32 rounded-full bg-purple-500/20 blur-2xl"></div>
            </div>
          </div>

          {/* Call to Action */}
          <div className="text-center mt-12">
            <Link href="/spiritual-journeys">
              <Button
                size="lg"
                className="bg-gradient-to-r from-gold-500 to-gold-600 hover:from-gold-600 hover:to-gold-700 text-black-950 font-medium px-8 py-6 rounded-full shadow-lg transition-all duration-300 hover:shadow-gold-500/20 hover:shadow-xl hover-lift group"
              >
                Discover the Complete Journey
                <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
}
