"use client";

import HeroSection from "@/components/sections/HeroSection";
import OverviewSection from "@/components/sections/OverviewSection";
import SpiritualJourneysOverviewSection from "@/components/sections/SpiritualJourneysOverviewSection";
import TestimonialsSection from "@/components/sections/TestimonialsSection";
import AboutSection from "@/components/sections/AboutSection";
import ContactSection from "@/components/sections/ContactSection";
import PackagesSection from "@/components/sections/PackagesSection";
import Footer from "@/components/layout/Footer";
import Header from "@/components/layout/Header";

export default function Home() {
  return (
    <div className="flex flex-col">
      <Header />

      <main className="flex-grow">
        {/* Hero section */}
        <div id="home">
          <HeroSection />
        </div>

        {/* Overview section */}
        <div id="overview">
          <OverviewSection />
        </div>

        {/* Spiritual Journeys Overview section */}
        <div id="spiritual-journeys-overview">
          <SpiritualJourneysOverviewSection />
        </div>

        {/* Experiences section */}
        <div id="experiences">
          <PackagesSection />
        </div>

        {/* Testimonials section */}
        <div id="testimonials">
          <TestimonialsSection />
        </div>

        {/* About section */}
        <div id="about">
          <AboutSection />
        </div>

        {/* Contact section */}
        <div id="contact">
          <ContactSection />
        </div>
      </main>

      <Footer />
    </div>
  );
}
